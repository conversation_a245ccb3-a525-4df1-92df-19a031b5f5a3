from openai import OpenAI
import os

# --- 配置客户端 ---
# 强烈推荐使用环境变量来设置密钥和地址，这样更安全
# 你可以在你的终端中设置:
# export OPENAI_API_KEY="你的API密钥"
# export OPENAI_API_BASE="你的代理地址"
# client = OpenAI() # 如果设置了环境变量，这里不需要传参

# 或者，如果你想在代码中直接指定（不推荐用于生产环境）
client = OpenAI(
    # # 在这里填入你从代理服务获得的 API 密钥
    # api_key="sk-...",
    # # 在这里填入你的代理地址，注意末尾通常需要加上 /v1
    # base_url="https://your-proxy-domain.com/v1",

    api_key="sk-HB1JrkhMfmjbhXLkC0A758Ca0b9a402185CeA568106f0c15",  # 在这里填入你从代理服务获得的 API 密钥
    base_url="https://www.gptapi.us/v1",  # 在这里填入你的代理地址，注意末尾通常需要加上 /v1
)

# --- 发起 API 请求 ---
try:
    # 1. 调用方法应为 client.chat.completions.create
    chat_completion = client.chat.completions.create(
        # 2. 模型名称正确
        model="gpt-4o-mini",
        # 3. 输入参数应为 "messages" 列表，而不是 "input" 字符串
        messages=[
            {
                "role": "user",
                "content": "Hello",
            }
        ]
    )

    # --- 处理并打印结果 ---
    # 4. 获取返回的文本内容，路径是固定的
    response_text = chat_completion.choices[0].message.content
    print(response_text)

except Exception as e:
    print(f"An error occurred: {e}")