from ../00_example/env_tool.py import client
text = f"""
您应该提供尽可能清晰、具体的指示，以表达您希望模型执行的任务。\
这将引导模型朝向所需的输出，并降低收到无关或不正确响应的可能性。\
不要将写清晰的提示词与写简短的提示词混淆。\
在许多情况下，更长的提示词可以为模型提供更多的清晰度和上下文信息，从而导致更详细和相关的输出。
"""

prompt = f"""
把用三个反引号括起来的文本总结成一句话。
```{text}```
"""

try:
    # 1. 调用方法应为 client.chat.completions.create
    chat_completion = client.chat.completions.create(
        # 2. 模型名称正确
        model="gpt-4o-mini",
        # 3. 输入参数应为 "messages" 列表，而不是 "input" 字符串
        messages=[
            {
                "role": "user",
                "content": "Hello",
            }
        ]
    )

    # --- 处理并打印结果 ---
    # 4. 获取返回的文本内容，路径是固定的
    response_text = chat_completion.choices[0].message.content
    print(response_text)

except Exception as e:
    print(f"An error occurred: {e}")