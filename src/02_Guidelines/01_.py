import sys
import os

# 添加 00_example 目录到 Python 路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '00_example')))

from env_tool import client
def main() -> None:
    text = f"""
    您应该提供尽可能清晰、具体的指示，以表达您希望模型执行的任务。\
    这将引导模型朝向所需的输出，并降低收到无关或不正确响应的可能性。\
    不要将写清晰的提示词与写简短的提示词混淆。\
    在许多情况下，更长的提示词可以为模型提供更多的清晰度和上下文信息，从而导致更详细和相关的输出。
    """

    prompt = f"""
    把用三个反引号括起来的文本总结成一句话。
    ```{text}```
    """

    try:
        # 调用OpenAI API进行文本总结
        chat_completion = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "user",
                    "content": prompt,
                }
            ]
        )

        # 获取并打印AI的回复
        response_text = chat_completion.choices[0].message.content
        print("AI总结结果:")
        print(response_text)

    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == "__main__":
    main()