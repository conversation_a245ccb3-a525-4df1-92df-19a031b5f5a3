from openai import OpenAI

# --- 配置客户端 ---
# 强烈推荐使用环境变量来设置密钥和地址，这样更安全
# 你可以在你的终端中设置:
# export OPENAI_API_KEY="你的API密钥"
# export OPENAI_API_BASE="你的代理地址"
# client = OpenAI() # 如果设置了环境变量，这里不需要传参

# 或者，如果你想在代码中直接指定（不推荐用于生产环境）
client = OpenAI(
    # # 在这里填入你从代理服务获得的 API 密钥
    # api_key="sk-...",
    # # 在这里填入你的代理地址，注意末尾通常需要加上 /v1
    # base_url="https://your-proxy-domain.com/v1",

    api_key="sk-HB1JrkhMfmjbhXLkC0A758Ca0b9a402185CeA568106f0c15",  # 在这里填入你从代理服务获得的 API 密钥
    base_url="https://www.gptapi.us/v1",  # 在这里填入你的代理地址，注意末尾通常需要加上 /v1
)